import { CandlestickChart } from '@/components/chart/candlestick-chart'
import { useGetMarginTradeKline } from '@/queries/kline'
import type { IKLineInfo } from '@/types/kline'
import { ResolutionKey, BAR_DURATION_MAP } from '@/components/chart/types'
import { useState, useMemo, useCallback, useEffect, useRef } from 'react'
import type {
  UTCTimestamp,
  LogicalRange,
  CandlestickData
} from 'lightweight-charts'
import KLineService from '@/services/kline'
import type { IChartApi } from 'lightweight-charts'
import { SUI_COIN_TYPE } from '@/lib/coin'
import {
  useKlineSSE,
  type SSEError,
  type ConnectionQuality
} from '@/hooks/use-kline-sse'

interface ChartProps {
  realtimeEnabled?: boolean
  realtimeInterval?: number // 已废弃，保持向后兼容
  coinType?: string
  useSSE?: boolean // 是否使用SSE进行实时更新
}

export function Chart({
  realtimeEnabled = false,
  realtimeInterval = 60000,
  coinType = SUI_COIN_TYPE,
  useSSE = true
}: ChartProps) {
  // 🔍 调试：只在coinType变化时记录
  const prevCoinTypeRef = useRef<string>()
  if (prevCoinTypeRef.current !== coinType) {
    console.log('🔄 Chart component coinType changed:', prevCoinTypeRef.current, '->', coinType)
    prevCoinTypeRef.current = coinType
  }

  const [bar, setBar] = useState<ResolutionKey>(ResolutionKey.Min15)
  const initialSize = 300
  const fetchSize = initialSize + 1
  const additionalSize = 50
  const [historicalData, setHistoricalData] = useState<IKLineInfo[]>([])
  const lastFromTime = useRef<number>(0)
  const isLoadingMore = useRef<boolean>(false)
  const hasMoreData = useRef<boolean>(false)
  const chartRef = useRef<IChartApi | null>(null)
  const seriesRef = useRef<{
    update: (data: CandlestickData<UTCTimestamp>) => void
  } | null>(null)
  const realtimeIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastUpdateTime = useRef<number>(0)
  const visibleRangeRef = useRef<{ from: number; to: number } | null>(null)
  const [initialDataLoaded, setInitialDataLoaded] = useState(false)

  // 🔥 防护措施：渲染次数限制，防止无限循环
  const renderCountRef = useRef<number>(0)
  const lastRenderTime = useRef<number>(0)

  renderCountRef.current++
  const now = Date.now()

  // 如果在1秒内渲染超过10次，可能存在无限循环
  if (now - lastRenderTime.current < 1000) {
    if (renderCountRef.current > 10) {
      console.error('🚨 Chart component render loop detected! Render count:', renderCountRef.current)
      // 可以在这里添加更多的错误处理逻辑
    }
  } else {
    // 重置计数器
    renderCountRef.current = 1
    lastRenderTime.current = now
  }

  // 🔥 关键修复：使用useRef存储disconnect函数，避免依赖数组问题
  const sseDisconnectRef = useRef<(() => void) | null>(null)

  // SSE Hook - 只有在初始数据加载完成且启用SSE时才连接
  const {
    connectionStatus,
    disconnect: sseDisconnect,
    connectionQuality,
    lastError,
    isOnline,
    browserSupported
  } = useKlineSSE({
    symbol: coinType,
    bar,
    enabled: realtimeEnabled && useSSE && initialDataLoaded && !!coinType,
    onKlineUpdate: useCallback((klineData: IKLineInfo) => {
      // 🔥 增强数据更新逻辑：检查是否是新数据且图表已准备就绪
      if (
        klineData.t > lastUpdateTime.current &&
        seriesRef.current &&
        chartRef.current
      ) {
        lastUpdateTime.current = klineData.t

        // 更新图表数据
        const newCandleData: CandlestickData<UTCTimestamp> = {
          time: (klineData.t / 1000) as UTCTimestamp,
          open: klineData.o,
          high: klineData.h,
          low: klineData.l,
          close: klineData.c
        }

        try {
          seriesRef.current.update(newCandleData)
          console.log(
            '✅ Chart data updated successfully - time:',
            newCandleData.time,
            'close:',
            newCandleData.close
          )
        } catch (error) {
          console.error('❌ Chart data update failed:', error)
        }
      } else if (!seriesRef.current || !chartRef.current) {
        console.warn('⚠️ Chart not ready, skipping data update')
      } else {
        console.log(
          '📊 Skipping old data - received:',
          klineData.t,
          'current latest:',
          lastUpdateTime.current
        )
      }
    }, []), // 🔥 空依赖数组，使用ref确保稳定性
    onConnected: useCallback((connectionId: string) => {
      console.log('🔗 K-line SSE connected successfully, ID:', connectionId)
    }, []),
    onError: useCallback((error: SSEError) => {
      console.error('❌ K-line SSE connection error:', {
        type: error.type,
        message: error.message,
        retryable: error.retryable,
        timestamp: new Date(error.timestamp).toISOString()
      })
    }, []),
    onQualityChange: useCallback((quality: ConnectionQuality) => {
      // 🔥 连接质量监控日志
      if (quality.messageCount % 10 === 0 && quality.messageCount > 0) {
        console.log('📊 Connection quality stats:', {
          latency: `${quality.latency}ms`,
          messages: quality.messageCount,
          errors: quality.errorCount,
          reconnects: quality.reconnectCount,
          lastMessage: new Date(quality.lastMessageTime).toISOString()
        })
      }
    }, [])
  })

  // 启动实时更新 (定时器模式 - 当不使用SSE时的备用方案)
  const startRealtimeUpdates = useCallback(() => {
    // 清除之前的定时器
    if (realtimeIntervalRef.current) {
      clearInterval(realtimeIntervalRef.current)
      realtimeIntervalRef.current = null
    }

    // 只有在不使用SSE且启用实时更新时才使用定时器
    if (realtimeEnabled && !useSSE && seriesRef.current) {
      realtimeIntervalRef.current = setInterval(async () => {
        try {
          // 获取最新的K线数据
          const now = Date.now()
          const fromTime = now - BAR_DURATION_MAP[bar] * 2 // 获取最近2个周期的数据

          const result = await KLineService.getMarginTradeKline.call({
            address: coinType,
            bar,
            fromTime,
            toTime: now,
            size: 10 // 只获取少量最新数据
          })

          if (result?.data && result.data.length > 0) {
            const latestData = result.data[result.data.length - 1]

            // 检查是否是新数据
            if (latestData.t > lastUpdateTime.current) {
              lastUpdateTime.current = latestData.t

              // 更新图表数据
              const newCandleData: CandlestickData<UTCTimestamp> = {
                time: (latestData.t / 1000) as UTCTimestamp,
                open: latestData.o,
                high: latestData.h,
                low: latestData.l,
                close: latestData.c
              }

              // 使用 series.update() 更新最新K线
              if (seriesRef.current) {
                seriesRef.current.update(newCandleData)
              }
            }
          }
        } catch (error) {
          console.error('Failed to fetch realtime data:', error)
        }
      }, realtimeInterval)
    }
  }, [realtimeEnabled, useSSE, realtimeInterval, bar, coinType])

  // 当实时更新开关或参数变化时，重新启动定时器 (仅适用于非SSE模式)
  useEffect(() => {
    if (!useSSE) {
      startRealtimeUpdates()
    }

    return () => {
      if (realtimeIntervalRef.current) {
        clearInterval(realtimeIntervalRef.current)
        realtimeIntervalRef.current = null
      }
    }
  }, [startRealtimeUpdates, useSSE])

  // 🔥 更新sseDisconnectRef
  sseDisconnectRef.current = sseDisconnect

  // 💡 重要：当时间周期变化时，重置状态
  useEffect(() => {
    console.log('🔄 Resetting chart state for bar change - bar:', bar)

    // 🔥 立即断开SSE连接以防止数据污染
    sseDisconnectRef.current?.()

    setHistoricalData([])
    setInitialDataLoaded(false)
    lastFromTime.current = 0
    isLoadingMore.current = false
    hasMoreData.current = true
    lastUpdateTime.current = 0
  }, [bar]) // 🔥 关键修复：移除sseDisconnect依赖，只监听bar变化

  const { toTime, fromTime } = useMemo(() => {
    const toTime = Date.now()
    const fromTime = toTime - initialSize * BAR_DURATION_MAP[bar]
    return { toTime, fromTime }
  }, [bar])

  const { data, isSuccess } = useGetMarginTradeKline(
    {
      address: coinType,
      bar,
      fromTime,
      toTime,
      size: fetchSize
    },
    {
      enabled: !!coinType // 只有当 coinType 有值时才启用查询
    }
  )

  // 🔥 新增：coinType变化时重置所有状态
  useEffect(() => {
    if (coinType) {
      console.log('🔄 coinType changed, resetting chart state:', coinType)
      // 重置所有状态
      setHistoricalData([])
      setInitialDataLoaded(false)
      lastFromTime.current = 0
      isLoadingMore.current = false
      hasMoreData.current = false
      lastUpdateTime.current = 0
      visibleRangeRef.current = null

      // 清理图表引用
      chartRef.current = null
      seriesRef.current = null
    }
  }, [coinType])

  // 当初始数据加载完成时，标记为已加载，这样可以启动SSE连接
  useEffect(() => {
    // 🔥 增强逻辑：确保coinType有效且数据真实加载完成
    if (isSuccess && data?.data && data.data.length > 0 && coinType) {
      // 💡 验证数据是否与当前coinType匹配（如果API返回address字段）
      const isDataValid = data.data.every(
        (item) => !item.address || item.address === coinType
      )

      if (isDataValid) {
        setInitialDataLoaded(true)
        // 设置最后更新时间为最新数据的时间
        const latestData = data.data[data.data.length - 1]
        if (latestData) {
          lastUpdateTime.current = latestData.t
        }
        console.log(
          '📊 Initial K-line data loaded, ready to start SSE connection - coinType:',
          coinType
        )
      } else {
        console.warn(
          '⚠️ Data does not match current coin pair, waiting for correct data'
        )
      }
    } else if (!coinType) {
      // 🔥 边界情况：coinType为空时确保不启动SSE
      setInitialDataLoaded(false)
      console.log('📊 coinType is empty, disabling SSE connection')
    }
  }, [isSuccess, data, coinType])

  const loadMoreHistoricalData = useCallback(async () => {
    if (isLoadingMore.current || !hasMoreData.current || !coinType) return

    console.log(
      '📈 Loading more historical data - coinType:',
      coinType,
      'bar:',
      bar
    )
    isLoadingMore.current = true
    if (lastFromTime.current === 0) {
      lastFromTime.current = fromTime
    }
    const historicalFromTime =
      lastFromTime.current - additionalSize * BAR_DURATION_MAP[bar]

    try {
      const result = await KLineService.getMarginTradeKline.call({
        address: coinType,
        bar,
        fromTime: historicalFromTime,
        toTime: lastFromTime.current,
        size: additionalSize
      })
      lastFromTime.current = historicalFromTime
      if (result && result.data && result.data.length > 0) {
        const currentVisibleRange = chartRef.current
          ?.timeScale()
          .getVisibleLogicalRange()
        if (currentVisibleRange) {
          visibleRangeRef.current = {
            from: currentVisibleRange.from + result.data.length,
            to: currentVisibleRange.to + result.data.length
          }
        }

        setHistoricalData((prev) => [...(result.data || []), ...prev])

        if (result.data.length < additionalSize) {
          hasMoreData.current = false
          console.log('📊 All historical data loaded')
        } else {
          console.log('📊 Historical data loaded, count:', result.data.length)
        }
      } else {
        hasMoreData.current = false
        console.log('📊 No more historical data available')
      }
    } catch (error) {
      console.error('❌ Failed to load historical data:', error)
      hasMoreData.current = false
    } finally {
      isLoadingMore.current = false
    }
  }, [bar, fromTime, coinType]) // 🔥 移除additionalSize依赖，它是常量

  const handleVisibleRangeChange = useCallback(
    (logicalRange: LogicalRange) => {
      if (
        Number(logicalRange.from) < 10 &&
        !isLoadingMore.current &&
        hasMoreData.current
      ) {
        loadMoreHistoricalData()
      }
    },
    [loadMoreHistoricalData]
  )

  // 🔥 关键修复：使用useCallback缓存回调函数，避免每次渲染都创建新函数
  const handleChartReady = useCallback((chart: IChartApi) => {
    console.log('📊 Chart ready callback triggered')
    chartRef.current = chart
  }, [])

  const handleSeriesReady = useCallback((series: {
    update: (data: CandlestickData<UTCTimestamp>) => void
  }) => {
    console.log('📊 Series ready callback triggered')
    seriesRef.current = series
  }, [])

  const chartData = useMemo((): CandlestickData<UTCTimestamp>[] => {
    const allData = [...historicalData, ...(data?.data || [])]
    return allData.map((item: IKLineInfo) => ({
      time: (item.t / 1000) as UTCTimestamp,
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c
    }))
  }, [data?.data, historicalData])

  useEffect(() => {
    if (visibleRangeRef.current && chartRef.current) {
      setTimeout(() => {
        chartRef.current
          ?.timeScale()
          .setVisibleLogicalRange(visibleRangeRef.current!)
        visibleRangeRef.current = null
      }, 0)
    }
  }, [historicalData])

  // 🔥 组件卸载时清理所有状态和定时器
  useEffect(() => {
    return () => {
      console.log('🧹 Chart component unmounting, cleaning up...')
      if (realtimeIntervalRef.current) {
        clearInterval(realtimeIntervalRef.current)
        realtimeIntervalRef.current = null
      }
      sseDisconnectRef.current?.()
      setHistoricalData([])
      setInitialDataLoaded(false)
    }
  }, [])

  return (
    <div className="relative">
      {/* SSE connection status indicator - enhanced version */}
      {realtimeEnabled && useSSE && (
        <div className="absolute top-2 right-2 z-10 flex flex-col gap-1">
          {/* Main connection status */}
          <div className="flex items-center gap-2 px-2 py-1 bg-black/50 rounded text-xs text-white">
            <div
              className={`w-2 h-2 rounded-full ${
                !browserSupported
                  ? 'bg-gray-400'
                  : !isOnline
                    ? 'bg-orange-500'
                    : connectionStatus === 'connected'
                      ? 'bg-green-500'
                      : connectionStatus === 'connecting'
                        ? 'bg-yellow-500 animate-pulse'
                        : connectionStatus === 'error'
                          ? 'bg-red-500'
                          : 'bg-gray-500'
              }`}
            />
            <span>
              {!browserSupported
                ? 'Browser Unsupported'
                : !isOnline
                  ? 'Network Offline'
                  : connectionStatus === 'connected'
                    ? 'Real-time Data'
                    : connectionStatus === 'connecting'
                      ? 'Connecting...'
                      : connectionStatus === 'error'
                        ? 'Connection Error'
                        : 'Disconnected'}
            </span>
            {/* Connection quality indicator */}
            {connectionStatus === 'connected' &&
              connectionQuality.latency > 0 && (
                <span className="text-gray-300">
                  ({connectionQuality.latency}ms)
                </span>
              )}
          </div>

          {/* Error information display */}
          {lastError && connectionStatus === 'error' && (
            <div className="px-2 py-1 bg-red-500/80 rounded text-xs text-white max-w-xs">
              <div className="font-medium">{lastError.type}</div>
              <div className="text-gray-200 truncate">{lastError.message}</div>
            </div>
          )}
        </div>
      )}

      <CandlestickChart
        data={chartData}
        currentResolution={bar}
        onResolutionChange={setBar}
        onVisibleRangeChange={handleVisibleRangeChange}
        onChartReady={handleChartReady}
        onSeriesReady={handleSeriesReady}
        isLoadingMore={isLoadingMore.current}
        className="border border-border-8 rounded-[10px] w-[936px] h-[400px] flex flex-col items-center justify-center p-4"
      />
    </div>
  )
}
