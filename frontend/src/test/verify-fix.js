/**
 * 验证Chart组件无限渲染循环修复的脚本
 *
 * 这个脚本通过静态分析代码来验证我们的修复是否正确
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取修复后的文件
const chartPath = path.join(__dirname, '../pages/margin-trade/components/chart.tsx');
const sseHookPath = path.join(__dirname, '../hooks/use-kline-sse.ts');

console.log('🔍 验证Chart组件无限渲染循环修复...\n');

// 验证Chart组件修复
function verifyChartFix() {
  console.log('📊 检查Chart组件修复...');

  try {
    const chartContent = fs.readFileSync(chartPath, 'utf8');

    const checks = [
      {
        name: '紧急停止机制',
        pattern: /emergencyStopRef\.current/,
        expected: true
      },
      {
        name: 'sseDisconnectRef使用',
        pattern: /sseDisconnectRef\.current\?\.\(\)/,
        expected: true
      },
      {
        name: 'dataLoadedRef防重复加载',
        pattern: /dataLoadedRef\.current/,
        expected: true
      },
      {
        name: '批量状态更新',
        pattern: /const resetStates = \(\) => \{/,
        expected: true
      },
      {
        name: '数据去重逻辑',
        pattern: /uniqueData = allData\.filter/,
        expected: true
      },
      {
        name: '移除sseDisconnect依赖',
        pattern: /\}, \[bar\]\) \/\/ 🔥 关键修复：移除sseDisconnect依赖/,
        expected: true
      }
    ];

    let passedChecks = 0;

    checks.forEach(check => {
      const found = check.pattern.test(chartContent);
      const status = found === check.expected ? '✅' : '❌';
      console.log(`  ${status} ${check.name}: ${found ? '已修复' : '未修复'}`);
      if (found === check.expected) passedChecks++;
    });

    console.log(`\n📊 Chart组件检查结果: ${passedChecks}/${checks.length} 通过\n`);
    return passedChecks === checks.length;

  } catch (error) {
    console.error('❌ 读取Chart组件文件失败:', error.message);
    return false;
  }
}

// 验证SSE Hook修复
function verifySSEHookFix() {
  console.log('🔗 检查SSE Hook修复...');

  try {
    const sseContent = fs.readFileSync(sseHookPath, 'utf8');

    const checks = [
      {
        name: '回调函数useRef存储',
        pattern: /onKlineUpdateRef = useRef\(onKlineUpdate\)/,
        expected: true
      },
      {
        name: '使用ref调用回调',
        pattern: /onKlineUpdateRef\.current\?\.\(klineData\)/,
        expected: true
      },
      {
        name: 'connect函数依赖优化',
        pattern: /\/\/ 🔥 关键修复：移除回调函数依赖，只保留稳定的值/,
        expected: true
      },
      {
        name: '移除onKlineUpdate依赖',
        pattern: /onKlineUpdate,/,
        expected: false // 应该被移除
      },
      {
        name: '移除onConnected依赖',
        pattern: /onConnected,/,
        expected: false // 应该被移除
      },
      {
        name: '移除onError依赖',
        pattern: /onError,/,
        expected: false // 应该被移除
      }
    ];

    let passedChecks = 0;

    checks.forEach(check => {
      const found = check.pattern.test(sseContent);
      const status = found === check.expected ? '✅' : '❌';
      const message = check.expected ?
        (found ? '已修复' : '未修复') :
        (found ? '仍存在问题' : '已移除');
      console.log(`  ${status} ${check.name}: ${message}`);
      if (found === check.expected) passedChecks++;
    });

    console.log(`\n🔗 SSE Hook检查结果: ${passedChecks}/${checks.length} 通过\n`);
    return passedChecks === checks.length;

  } catch (error) {
    console.error('❌ 读取SSE Hook文件失败:', error.message);
    return false;
  }
}

// 执行验证
const chartFixed = verifyChartFix();
const sseFixed = verifySSEHookFix();

console.log('🎯 总体修复结果:');
console.log(`  📊 Chart组件: ${chartFixed ? '✅ 已修复' : '❌ 需要进一步修复'}`);
console.log(`  🔗 SSE Hook: ${sseFixed ? '✅ 已修复' : '❌ 需要进一步修复'}`);

if (chartFixed && sseFixed) {
  console.log('\n🎉 所有修复验证通过！无限渲染循环问题应该已经解决。');
  console.log('\n📋 修复总结:');
  console.log('  1. ✅ 使用useRef存储回调函数，避免依赖数组问题');
  console.log('  2. ✅ 优化useEffect依赖数组，移除不稳定的函数引用');
  console.log('  3. ✅ 添加紧急停止机制，防止极端情况下的无限循环');
  console.log('  4. ✅ 实现批量状态更新，减少渲染次数');
  console.log('  5. ✅ 添加数据去重和验证逻辑');
  console.log('  6. ✅ 使用ref跟踪状态，避免循环依赖');
} else {
  console.log('\n⚠️  部分修复可能不完整，请检查上述失败的项目。');
}

console.log('\n🔧 建议的测试步骤:');
console.log('  1. 启动开发服务器');
console.log('  2. 打开包含Chart组件的页面');
console.log('  3. 观察浏览器控制台，确认没有渲染循环错误');
console.log('  4. 切换不同的时间周期和币种，验证功能正常');
console.log('  5. 监控组件渲染性能');